import React, { useEffect, useState } from 'react'
import * as SharedStyled from '../../../../../styles/styled'
import { Form, Formik } from 'formik'
import CustomSelect from '../../../../../shared/customSelect/CustomSelect'
import AutoComplete from '../../../../../shared/autoComplete/AutoComplete'
import Button from '../../../../../shared/components/button/Button'
import Checkbox from '../../../../../shared/checkbox/Checkbox'
import { SharedDateAndTime } from '../../../../../shared/date/SharedDateAndTime'
import {
  dayjsFormat,
  extractPermissionByName,
  generateUUID,
  getEnumValue,
  getKeysFromObjects,
  getValueByKeyAndMatch,
  hasValues,
  isSuccess,
  notify,
} from '../../../../../shared/helpers/util'
import { CustomModal } from '../../../../../shared/customModal/CustomModal'
import { useSelector } from 'react-redux'
import { getActionMembers, getSalesActionByMemberId, updateActivity } from '../../../../../logic/apis/sales'
import * as Yup from 'yup'
import ActionModal from '../../../../opportunity/components/actionModal/ActionModal'
import {
  completeContactAction,
  createActionForContactOpp,
  createContactAction,
  getActionsForContactOpp,
  updateActionForContactOpp,
  updateActivity as updateActivityForContactOpp,
} from '../../../../../logic/apis/contact'
import EditIcon from '../../../../../assets/newIcons/edit.svg'

import Pill from './Pill'
import { RoundButton } from '../../../../../shared/components/button/style'
import { colors } from '../../../../../styles/theme'
import { ActionHistoryCont, TodoNextCont } from '../style'

interface I_Action {
  type: string
  body: string
  completedBy: string
  assignTo?: string
  due: string
  _id?: string
  oppId?: string
  PO?: string
  num?: string
  contactId?: string
  stageGroup?: string
}

interface I_FormProps {
  contactData: any
  contactOrOppId: string | undefined
  setContactData: React.Dispatch<React.SetStateAction<any | undefined>>
  fetchActivity: () => Promise<void>
  isContact?: boolean
}

interface I_NextAction {
  _id: string
  type: string
  body: string
  due: string
  createdBy: string
  assignTo?: string
  createdAt: string
}

const ToDoNextProfile: React.FC<I_FormProps> = (props) => {
  const [todoCheck, setTodoCheck] = useState(false)
  const [todoCheckObj, setTodoCheckObj] = useState<{ [key: string]: boolean }>({})
  const [editTodo, setEditTodo] = useState(false)
  const [actionsForToDoNext, setActionsForToDoNext] = useState<any>([])
  const [allActionData, setAllActionData] = useState<{
    nextAction: I_Action[]
    history: I_Action[]
  }>()
  const hasTodoCheckObj = Object.values(todoCheckObj).some(Boolean)
  const [toDoLoading, setToDoLoading] = useState(false)
  const [actionModal, setActionModal] = useState(false)
  const [autoFillValues, setAutoFillValues] = useState({ type: '', name: '' })
  const [autoFillValuesFromChild, setAutoFillValuesFromChild] = useState({ type: '', name: '' })
  const [salesPersonDrop, setSalesPersonDrop] = useState<any[]>([])
  const [showHistory, setShowHistory] = useState(false)
  // const [selectedAction, setSelectedAction] = useState<Partial<I_Action>>({})
  const [isNewAction, setIsNewAction] = useState(false)

  const [initTodoData, setInitTodoData] = useState<I_Action>({
    due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
    type: '',
    completedBy: '',
    body: '',
  })
  const { contactData, contactOrOppId, setContactData, isContact = false, fetchActivity } = props

  const globalSelector = useSelector((state: any) => state)
  const { currentMember, positionDetails } = globalSelector.company

  const hasOppManagedFullPermission =
    hasValues(positionDetails) && extractPermissionByName(positionDetails, 'actions')?.permissions < 3

  useEffect(() => {
    if (currentMember?._id) {
      fetchActions()
      getActivePositionMembers()

      getAllActionsForContactOpp()
    }
  }, [currentMember])

  const getAllActionsForContactOpp = async () => {
    try {
      const response = await getActionsForContactOpp({ contactOppId: contactOrOppId!, isContact })
      if (isSuccess(response)) {
        setAllActionData(response?.data?.data)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const fetchActions = async () => {
    try {
      const res = await getSalesActionByMemberId(currentMember?._id, false)
      if (isSuccess(res)) {
        const { actions } = res.data.data.salesAction
        setActionsForToDoNext(actions)
      }
    } catch (error) {
      console.log({ error })
    }
  }

  const getActivePositionMembers = async () => {
    try {
      const response = await getActionMembers({}, false)
      if (isSuccess(response)) {
        setSalesPersonDrop(response?.data?.data?.memberData)
      } else notify(response?.data?.message, 'error')
    } catch (err) {
      console.log('GET POSITION MEMBERS FAILED', err)
    }
  }

  // useEffect(() => {
  //   if (allActionData?.nextAction?.length) {
  //     const newTodoCheckObj: { [key: string]: boolean } = {}
  //     allActionData?.nextAction?.forEach((action: any) => {
  //       newTodoCheckObj[action?._id] = false
  //     })
  //     setTodoCheckObj(newTodoCheckObj)
  //   }
  // }, [allActionData?.nextAction?.length])

  const onTodoComplete = async (nextAction: I_Action) => {
    const checkedActionId = Object.keys(todoCheckObj).find((key) => todoCheckObj[key])!

    const selectedAction = nextAction?._id
      ? allActionData?.nextAction?.find((action: any) => action?._id! === nextAction?._id!)
      : allActionData?.nextAction?.find((action: any) => action?._id === checkedActionId)

    const actionId = actionsForToDoNext?.find((action: { name: string }) => action?.name === nextAction?.body)?._id

    const assignTo =
      salesPersonDrop?.find((person: any) => person.name === nextAction.assignTo)?._id || currentMember?._id

    const isContactAction = !selectedAction?.oppId

    let nextActionData: I_NextAction = {
      _id: actionId ?? generateUUID(),
      body: nextAction.body,
      createdAt: new Date().toISOString(),
      createdBy: currentMember._id,
      due: new Date(nextAction.due).toISOString(),
      type: nextAction.type,
      assignTo: assignTo ?? undefined,
    }

    try {
      setToDoLoading(true)

      // Edit Action

      if (!hasTodoCheckObj && allActionData?.nextAction?.length && !isNewAction) {
        const responseForEdit = await createActionForContactOpp({
          contactOppId: isContactAction ? selectedAction?.contactId! : selectedAction?.oppId!,
          isContact: isContactAction,
          data: {
            body: nextAction.body,
            currDate: new Date(),
            dueDate: new Date(nextAction.due),
            memberId: currentMember._id!,
            assignTo: assignTo ?? undefined,
            type: nextAction.type,
            id: actionId ?? generateUUID(),
          },
        })

        if (isSuccess(responseForEdit)) {
          // setContactData((prev: any) => ({ ...prev, nextAction: nextActionData }))

          getAllActionsForContactOpp()
          if (selectedAction?._id) {
            if (isContact) {
              const res = await updateActivityForContactOpp(
                {
                  id: contactOrOppId!,
                  memberId: currentMember._id!,
                  body: `Edited:  ${selectedAction?.body} to ${nextAction.body} | Due date: ${new Date(
                    selectedAction?.due
                  ).toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })} ${
                    selectedAction?.due
                      ? new Date(selectedAction?.due).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: 'numeric',
                        })
                      : ''
                  } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                    month: '2-digit',
                    day: '2-digit',
                    year: 'numeric',
                  })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: 'numeric',
                  })}`,
                  currDate: new Date().toISOString(),
                },
                contactOrOppId!
              )

              if (isSuccess(res)) {
                notify('Action Updated', 'success')
                fetchActivity()
              }
            } else {
              const res = await updateActivity({
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Edited:  ${selectedAction?.body} to ${nextAction.body} | Due date: ${new Date(
                  selectedAction?.due
                ).toLocaleDateString('en-US', {
                  month: '2-digit',
                  day: '2-digit',
                  year: 'numeric',
                })} ${
                  selectedAction?.due
                    ? new Date(selectedAction?.due).toLocaleTimeString('en-US', {
                        hour: '2-digit',
                        minute: 'numeric',
                      })
                    : ''
                } to ${new Date(nextAction.due).toLocaleDateString('en-US', {
                  month: '2-digit',
                  day: '2-digit',
                  year: 'numeric',
                })} ${new Date(nextAction.due).toLocaleTimeString('en-US', {
                  hour: '2-digit',
                  minute: 'numeric',
                })}`,
                currDate: new Date().toISOString(),
              })

              if (isSuccess(res)) {
                notify('Action Updated', 'success')
                fetchActivity()
              }
            }
          }
        } else {
          throw new Error(responseForEdit?.data?.message)
        }
      } else {
        // action api

        // Complete and Create Action

        const isActionInAlreadyInHistory = allActionData?.history?.find(
          (action: any) => action?._id === selectedAction?._id
        )?._id

        let completeResponse

        if (selectedAction?._id) {
          completeResponse = await updateActionForContactOpp({
            data: {
              body: selectedAction?.body,
              currDate: new Date(),
              dueDate: selectedAction?.due,
              memberId: currentMember._id!, //UserId##
              assignTo: assignTo ?? undefined,
              type: selectedAction?.type,
              id: isActionInAlreadyInHistory ? generateUUID()! : selectedAction?._id,
            },
            contactOppId: isContactAction ? selectedAction?.contactId! : selectedAction?.oppId!,
            isContact: isContactAction,
          })
        }

        const nextActionId = actionsForToDoNext?.find(
          (action: { name: string }) => action?.name === nextAction?.body
        )?._id

        const response = await createActionForContactOpp({
          contactOppId: isNewAction ? contactOrOppId! : isContactAction ? contactOrOppId! : selectedAction?.oppId!,
          isContact: isNewAction ? isContact : isContactAction,
          data: {
            body: nextAction.body,
            currDate: new Date(),
            dueDate: nextAction.due,
            memberId: currentMember._id!,
            type: nextAction.type,
            assignTo: assignTo ?? undefined,
            id: isNewAction ? generateUUID()! : nextActionId ?? generateUUID(),
          },
        })

        getAllActionsForContactOpp()

        if (isSuccess(response) && isContact) {
          // setContactData((prev: any) => ({ ...prev, nextAction: nextActionData }))
          if (selectedAction?._id) {
            const res1 = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Completed action ${selectedAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )
            const res2 = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Created new action ${nextAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivityForContactOpp(
              {
                id: contactOrOppId!,
                memberId: currentMember._id!,
                body: `Created new action ${nextAction?.body}`,
                currDate: new Date().toISOString(),
              },
              contactOrOppId!
            )
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        } else {
          if (selectedAction?._id) {
            const res1 = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Completed action ${selectedAction?.body}`,
              currDate: new Date().toISOString(),
            })
            const res2 = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextActionData.body}`,
              currDate: new Date().toISOString(),
            })

            if (isSuccess(res1) && isSuccess(res2)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          } else {
            const res = await updateActivity({
              id: contactOrOppId!,
              memberId: currentMember._id!,
              body: `Created new action ${nextAction.body}`,
              currDate: new Date().toISOString(),
            })
            if (isSuccess(res)) {
              notify('Action Created', 'success')
              fetchActivity()
            }
          }
        }
      }
      setTodoCheck(false)
      setTodoCheckObj({})
      setEditTodo(false)
    } catch (err) {
      console.log('ACTIONS ERR', err)
    } finally {
      setToDoLoading(false)
      setIsNewAction(false)
    }
  }

  const onEditCancel = () => {
    setInitTodoData({
      due: dayjsFormat(new Date(), 'YYYY-MM-DDTHH:mm'),
      type: '',
      completedBy: '',
      body: '',
      _id: '',
      assignTo: '',
    })

    // if (selectedAction?._id) {
    setEditTodo(false)
    setTodoCheck(false)
    setTodoCheckObj({})
    // }
  }
  const onEditTodo = (action: I_Action) => {
    // if (contactData && contactData?.nextAction)
    //   setInitTodoData({
    //     due: dayjsFormat(contactData?.nextAction.due, 'YYYY-MM-DDTHH:mm'),
    //     body: contactData!.nextAction.body,
    //     type: contactData!.nextAction.type,
    //     completedBy: currentMember._id!,
    //     assignTo: salesPersonDrop?.find((person: any) => person._id === contactData?.nextAction?.assignTo)?.name,
    //   })
    // setSelectedAction(action)

    if (action) {
      setInitTodoData({
        _id: action?._id,
        due: dayjsFormat(action?.due, 'YYYY-MM-DDTHH:mm'),
        body: action?.body,
        type: action?.type,
        completedBy: currentMember._id!,
        assignTo: salesPersonDrop?.find((person: any) => person._id === action?.assignTo)?.name,
      })
    }

    setEditTodo((prev) => !prev)
  }

  const todoSchema = Yup.object().shape({
    body: Yup.string().required('Required'),
    due: Yup.string().required('Required'),
    type: Yup.string().required('Required'),
  })
  return (
    <TodoNextCont>
      <div>
        <SharedStyled.ContentHeader textAlign="left" as="h3">
          To Do Next
        </SharedStyled.ContentHeader>
        {toDoLoading ? (
          <SharedStyled.CardSkeleton height="200px"></SharedStyled.CardSkeleton>
        ) : (
          <div className="todo-item">
            {hasTodoCheckObj || editTodo || !allActionData?.nextAction ? (
              <div style={{ width: '100%' }}>
                <Formik
                  initialValues={initTodoData}
                  onSubmit={onTodoComplete}
                  validationSchema={todoSchema}
                  enableReinitialize={true}
                  validateOnChange={true}
                  validateOnBlur={false}
                >
                  {({ touched, errors, resetForm, values, setFieldValue }) => {
                    useEffect(() => {
                      if (autoFillValuesFromChild.type !== '' && autoFillValuesFromChild.name !== '') {
                        setFieldValue('type', autoFillValuesFromChild.type)
                        setFieldValue('body', autoFillValuesFromChild.name)
                      }
                    }, [autoFillValuesFromChild, contactData])

                    useEffect(() => {
                      if (values.type) {
                        setAutoFillValues((prev) => ({
                          ...prev,
                          type: values.type,
                        }))
                      }
                    }, [values.type])

                    useEffect(() => {
                      if (salesPersonDrop?.length && hasOppManagedFullPermission && isNewAction) {
                        setFieldValue(
                          'assignTo',
                          salesPersonDrop?.find((person: any) => person._id === currentMember?._id)?.name
                        )
                        return
                      }
                      if (salesPersonDrop?.length && hasOppManagedFullPermission && hasTodoCheckObj) {
                        setFieldValue(
                          'assignTo',
                          salesPersonDrop?.find((person: any) => person._id === currentMember?._id)?.name
                        )
                      }
                    }, [salesPersonDrop?.length, hasTodoCheckObj, isNewAction])

                    return (
                      <Form>
                        {/* stepObject */}
                        <SharedStyled.TwoInputDiv>
                          <SharedStyled.FlexBox width="100%" gap="12px">
                            <AutoComplete
                              value={values?.body}
                              options={getKeysFromObjects(actionsForToDoNext ?? [], 'name') ?? []}
                              dropdownHeight={'300px'}
                              labelName="Next Action"
                              stateName="body"
                              setFieldValue={setFieldValue}
                              error={touched.body && errors.body ? true : false}
                              onAddClick={(val: string) => {
                                setAutoFillValues((prev) => ({ ...prev, name: val }))
                                setActionModal(true)
                              }}
                              showAddOption
                              setValueOnClick={(val: string) => {
                                setFieldValue(`type`, getValueByKeyAndMatch('type', val, `name`, actionsForToDoNext))
                              }}
                            />
                          </SharedStyled.FlexBox>
                        </SharedStyled.TwoInputDiv>
                        <div
                          style={{
                            display: 'grid',
                            gap: '10px',
                            gridTemplateColumns: hasOppManagedFullPermission ? '1fr 1fr 1fr' : '1fr 2fr',
                            alignItems: 'flex-start',
                          }}
                        >
                          <CustomSelect
                            labelName="Select Type"
                            stateName="type"
                            error={touched.type && errors.type ? true : false}
                            value={values.type}
                            dropDownData={['Task', 'Call', 'Email', 'Text']}
                            setValue={() => {}}
                            setFieldValue={setFieldValue}
                            innerHeight="52px"
                            margin="10px 0 0 0"
                          />

                          <SharedDateAndTime
                            value={values.due}
                            labelName={'Due Date/Time'}
                            stateName="due"
                            setFieldValue={setFieldValue}
                            error={touched.due && errors.due ? true : false}
                          />

                          {hasOppManagedFullPermission && (
                            <CustomSelect
                              labelName="Assign To:"
                              stateName="assignTo"
                              error={touched.assignTo && errors.assignTo ? true : false}
                              value={values.assignTo ? values.assignTo : ''}
                              dropDownData={salesPersonDrop?.map((val) => val.name)}
                              setValue={() => {}}
                              setFieldValue={setFieldValue}
                              innerHeight="52px"
                              margin="10px 0 0 0"
                            />
                          )}
                        </div>

                        <SharedStyled.FlexBox width="100%" gap="12px" margin="24px 0 0 0">
                          <Button type="submit" className="fit">
                            Save Action
                          </Button>
                          <Button
                            type="button"
                            onClick={() => (allActionData?.nextAction?.length ? onEditCancel() : setEditTodo(false))}
                            className="fit outline"
                          >
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </Form>
                    )
                  }}
                </Formik>
              </div>
            ) : null}

            {!editTodo &&
            (isContact
              ? !allActionData?.nextAction?.filter((action: any) => action?.contactId === contactOrOppId).length
              : !allActionData?.nextAction?.filter((action: any) => action?.oppId === contactOrOppId).length) ? (
              <SharedStyled.FlexRow margin="24px 0 0 0">
                <Checkbox onChange={() => {}} disabled value={true} cursor="pointer" />

                <div className="checkbox-item">
                  <div>
                    <SharedStyled.FlexCol gap="2px">
                      <SharedStyled.Text fontSize="14px" fontWeight="bold">
                        <>No Action</>&nbsp; - &nbsp;{' '}
                        <span
                          style={{
                            color: colors.darkBlue,
                            cursor: 'pointer',
                            textDecoration: 'underline',
                          }}
                          onClick={() => {
                            setIsNewAction(true)
                            setTodoCheckObj({})
                            setEditTodo(true)
                          }}
                        >
                          Create
                        </span>
                      </SharedStyled.Text>

                      <div>
                        <Pill
                          path={''}
                          numVal={contactData?.PO ? contactData?.num : undefined}
                          text={isContact ? 'Contact' : `${contactData?.PO}-${contactData?.num}`}
                        />
                      </div>
                    </SharedStyled.FlexCol>
                  </div>
                </div>
              </SharedStyled.FlexRow>
            ) : null}

            {allActionData?.nextAction?.length && (!editTodo || isNewAction) ? (
              <>
                {/*  <div className="todo-container">
                  <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between">
                    <SharedStyled.FlexRow>
                      <Checkbox
                        onChange={() => {
                          setTodoCheck((prev) => !prev)
                        }}
                        value={todoCheck}
                        cursor="pointer"
                      />

                      <div className="checkbox-item">
                        <div className={todoCheck ? 'strike' : ''}>
                          <SharedStyled.FlexCol>
                            <SharedStyled.Text fontSize="14px" fontWeight="bold">
                              <>{contactData?.nextAction?.body}</>
                            </SharedStyled.Text>

                            <div>
                              <SharedStyled.Text fontSize="12px">
                                <>{contactData.nextAction?.type} on </>
                              </SharedStyled.Text>
                              {contactData.nextAction?.due ? (
                                <SharedStyled.Text fontSize="12px">
                                  <>
                                    {dayjsFormat(contactData?.nextAction?.due, 'M/D/YY')} @{' '}
                                    {dayjsFormat(contactData?.nextAction?.due, 'h:mm a')}
                                  </>
                                </SharedStyled.Text>
                              ) : (
                                ''
                              )}
                            </div>

                            <div>
                              <SharedStyled.Text color="grey" fontSize="12px">
                                <>
                                  {salesPersonDrop?.find(
                                    (person: any) => person._id === contactData?.nextAction.assignTo
                                  )?.name || '--'}
                                </>
                                &emsp;
                              </SharedStyled.Text>
                              <SharedStyled.Text
                                padding="2px 8px"
                                backgroundColor="#bcbcbc"
                                color="white"
                                fontSize="12px"
                                fontWeight="bold"
                                borderRadius="5px"
                              >
                                CONTACT
                              </SharedStyled.Text>
                            </div>
                          </SharedStyled.FlexCol>
                        </div>
                      </div>
                    </SharedStyled.FlexRow>

                    <div>
                      {!todoCheck && (
                        <SharedStyled.IconContainer
                          className="edit"
                          onClick={() => {
                            onEditTodo()
                          }}
                        >
                          <EditIcon />
                          /~ Edit ~/
                        </SharedStyled.IconContainer>
                      )}
                    </div>
                  </SharedStyled.FlexRow>
                </div>*/}
                {/* ================= New ================= */}

                {allActionData?.nextAction?.map((action: any) => (
                  <div className="todo-container" key={action?._id}>
                    <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between">
                      <SharedStyled.FlexRow>
                        <input
                          key={action?._id}
                          type="radio"
                          name="todoAction"
                          onChange={(e) => {
                            e.stopPropagation()
                            const newTodoCheckObj: Record<string, boolean> = {}

                            allActionData?.nextAction?.forEach((item: any) => {
                              newTodoCheckObj[item._id] = item._id === action._id ? e.target.checked : false
                            })

                            setTodoCheckObj(newTodoCheckObj)
                          }}
                          checked={todoCheckObj[action?._id] ?? false}
                          style={{ cursor: 'pointer' }}
                        />

                        <div className="checkbox-item">
                          <div className={todoCheckObj?.[action?._id] ? 'strike' : ''}>
                            <SharedStyled.FlexCol gap="2px">
                              <SharedStyled.Text fontSize="14px" fontWeight="bold">
                                <>{action?.body}</>
                              </SharedStyled.Text>

                              <div>
                                <SharedStyled.Text fontSize="12px">
                                  <>{action?.type} on </>
                                </SharedStyled.Text>
                                {action?.due ? (
                                  <SharedStyled.Text fontSize="12px">
                                    <>
                                      {dayjsFormat(action?.due, 'M/D/YY')} @ {dayjsFormat(action?.due, 'h:mm a')}
                                    </>
                                  </SharedStyled.Text>
                                ) : (
                                  ''
                                )}
                              </div>

                              <div>
                                <SharedStyled.Text color="grey" fontSize="12px">
                                  <>
                                    {salesPersonDrop?.find((person: any) => person._id === action.assignTo)?.name ||
                                      '--'}
                                  </>
                                  &emsp;
                                </SharedStyled.Text>
                                <Pill
                                  margin="0 0 0 10px"
                                  numVal={action?.oppId ? action?.num : undefined}
                                  path={
                                    action?.oppId
                                      ? `/${getEnumValue(action?.stageGroup)}/opportunity/${action?.oppId}`
                                      : `/contact/profile/${action?.contactId}/false`
                                  }
                                  text={action?.oppId ? `${action?.PO}-${action?.num}` : 'Contact'}
                                />
                              </div>
                            </SharedStyled.FlexCol>
                          </div>
                        </div>
                      </SharedStyled.FlexRow>

                      <div>
                        {!hasTodoCheckObj && (
                          <RoundButton
                            className="edit"
                            onClick={(e) => {
                              e.stopPropagation()

                              onEditTodo(action)
                            }}
                          >
                            <img src={EditIcon} alt="edit icon" />
                          </RoundButton>
                        )}
                      </div>
                    </SharedStyled.FlexRow>
                  </div>
                ))}

                <SharedStyled.FlexRow justifyContent="flex-end" margin="10px 0">
                  {allActionData?.history?.length ? (
                    <span
                      onClick={() => setShowHistory((prev) => !prev)}
                      style={{ cursor: 'pointer', fontSize: '14px' }}
                    >
                      {showHistory ? 'Hide' : 'Show'} action history
                    </span>
                  ) : null}
                </SharedStyled.FlexRow>

                {!showHistory ? null : (
                  <ActionHistoryCont>
                    {allActionData?.history?.map((action) => (
                      <div className="todo-container">
                        <SharedStyled.FlexRow margin="10px 0 0 0" justifyContent="space-between">
                          <SharedStyled.FlexRow>
                            {/* <Checkbox
                              onChange={() => {
                                setTodoCheck((prev) => !prev)
                              }}
                              value={todoCheck}
                              cursor="pointer"
                            /> */}

                            <div
                              style={{
                                width: '30px',
                              }}
                            />

                            <div className="checkbox-item">
                              <div>
                                <SharedStyled.FlexCol gap="2px">
                                  <SharedStyled.Text fontSize="14px" fontWeight="bold">
                                    <>{action?.body}</>
                                  </SharedStyled.Text>

                                  <div>
                                    <SharedStyled.Text fontSize="12px">
                                      <>{action?.type} on </>
                                    </SharedStyled.Text>
                                    {action?.due ? (
                                      <SharedStyled.Text fontSize="12px">
                                        <>
                                          {dayjsFormat(action?.due, 'M/D/YY')} @ {dayjsFormat(action?.due, 'h:mm a')}
                                        </>
                                      </SharedStyled.Text>
                                    ) : (
                                      ''
                                    )}
                                  </div>

                                  <div>
                                    <SharedStyled.Text color="grey" fontSize="12px">
                                      <>
                                        {salesPersonDrop?.find((person: any) => person._id === action.assignTo)?.name ||
                                          '--'}
                                      </>
                                      &emsp;
                                    </SharedStyled.Text>

                                    <Pill
                                      margin="0 0 0 10px"
                                      numVal={action?.oppId ? action?.num : undefined}
                                      path={
                                        action?.oppId
                                          ? `/${getEnumValue(action?.stageGroup!)}/opportunity/${action?.oppId}`
                                          : `/contact/profile/${action?.contactId}/false`
                                      }
                                      text={action?.oppId ? `${action?.PO}-${action?.num}` : 'Contact'}
                                    />
                                  </div>
                                </SharedStyled.FlexCol>
                              </div>
                            </div>
                          </SharedStyled.FlexRow>

                          {/* <div>
                            {!todoCheck && (
                              <SharedStyled.IconContainer
                                className="edit"
                                onClick={() => {
                                  onEditTodo()
                                }}
                              >
                                <EditIcon />
                                /~ Edit ~/
                              </SharedStyled.IconContainer>
                            )}
                          </div>*/}
                        </SharedStyled.FlexRow>
                      </div>
                    ))}
                  </ActionHistoryCont>
                )}
                {/* ================= New ================= */}
              </>
            ) : null}
          </div>
        )}
      </div>
      <CustomModal show={actionModal}>
        <ActionModal
          onClose={() => setActionModal(false)}
          onComplete={fetchActions}
          values={autoFillValues}
          setAutoFillValuesFromChild={setAutoFillValuesFromChild}
        />
      </CustomModal>
    </TodoNextCont>
  )
}

export default ToDoNextProfile
