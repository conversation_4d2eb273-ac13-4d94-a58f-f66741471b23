import React, { useEffect, useState } from 'react'
import * as Styled from './style'
import * as SharedStyled from '../../../../styles/styled'
import { Field, Form, Formik } from 'formik'
import { InputWithValidation } from '../../../../shared/inputWithValidation/InputWithValidation'
import { TextArea } from '../../../../shared/textArea/TextArea'
import { I_Comment } from '../assessmentForm/AssessmentForm'
import {
  dayjsFormat,
  deleteElementAtIndex,
  generateUUID,
  getDataFromLocalStorage,
  getEnumValue,
  isSuccess,
  isWithinHour,
  notify,
} from '../../../../shared/helpers/util'
import { createComment, deleteCommentApi, updateCommentApi, updateOpportunity } from '../../../../logic/apis/sales'
import { I_Opportunity } from '../../Opportunity'
import { useParams } from 'react-router-dom'
import * as Yup from 'yup'
import { useSelector } from 'react-redux'
import { colors } from '../../../../styles/theme'
import ReactMarkdown from 'react-markdown'
import Button from '../../../../shared/components/button/Button'
import { EditIcon } from '../../../../assets/icons/EditIcon'
import { CrossIcon } from '../../../../assets/icons/CrossIcon'
import remarkGfm from 'remark-gfm'
import { Nue, StorageKey } from '../../../../shared/helpers/constants'
import TextDiffViewer from './TextDiffViewer'
import Pill from '../../../contact/components/contactProfile/components/Pill'
import Toggle from '../../../../shared/toggle/Toggle'

const commentSchema = Yup.object().shape({
  comment: Yup.string().required("Can't be empty!"),
})

interface I_AllComments {
  body: string
  createdAt: string
  createdBy: string
  userName?: string
  _id: string
  name?: string
  type?: string
}

const Comments: React.FC<{
  comments: I_Comment[]
  initFetchOpportunity: any
}> = ({ comments, initFetchOpportunity }) => {
  const [allComments, setAllComments] = useState<I_AllComments[]>([])
  const [editing, setEditing] = useState(-1) // index of editing comment
  const { oppId } = useParams()
  const [commentFlag, setCommentFlag] = useState(false)
  const [commentFlag1, setCommentFlag1] = useState(false)
  const [toggleEdit, setToggleEdit] = useState<{ [key: string]: boolean }>({})
  const [isToggleOn, setIsToggleOn] = useState(false)

  const globalSelector = useSelector((state: any) => state)
  const { currentCompany, currentMember, positionDetails } = globalSelector.company
  const initComment = {
    comment: getDataFromLocalStorage(`comment-${oppId}`) || '',
  }
  const initUpdateComment = {
    comment: '',
    _id: '',
  }
  const toggleTableData = (type: string) => {
    setToggleEdit((prevState) => ({
      ...prevState,
      [type]: !prevState[type],
    }))
  }

  const submitComment = async (values: typeof initComment) => {
    try {
      setCommentFlag(true)
      setCommentFlag1(true)

      const newComment = {
        createdBy: currentMember._id,
        body: values.comment,
        createdAt: new Date().toISOString(),
        userName: '',
        _id: '',
      }
      const newComments = [newComment, ...comments]
      const response = await createComment({
        body: values.comment,
        currDate: new Date().toISOString(),
        // dueDate: new Date().toISOString(),
        memberId: currentMember._id!,
        oppId: oppId!,
        // type: 'comment',
        id: generateUUID()!,
      })
      if (isSuccess(response)) {
        initFetchOpportunity()
        localStorage.removeItem(`comment-${oppId}`)
        // updateComment(newComments)
        notify('Added comment', 'success')
        setCommentFlag(false)
        setCommentFlag1(false)
        localStorage.removeItem(`comment-${oppId}`)
      } else {
        notify('Failed to add comment', 'error')
      }
    } catch (err) {
      console.log('COMMENTING ERR', err)
      setCommentFlag(false)
      setCommentFlag1(false)
    }
  }

  const editCommentComplete = async (values: typeof initUpdateComment) => {
    try {
      setCommentFlag(true)
      console.log({ values }, comments)
      const newComment = {
        createdBy: currentMember._id!,
        body: values.comment,
        createdAt: new Date().toISOString(),
        // userName: comments[editing]?.userName,
        _id: '',
      }
      const newComments = [newComment, ...comments]
      // newComments[editing] = newComment
      // {
      // }
      const response = await updateCommentApi({
        id: values?._id ?? '',
        memberId: currentMember._id!, //UserId##
        oppId: oppId!,
        body: values?.comment,
        currDate: new Date().toISOString(),
      })
      if (isSuccess(response)) {
        initFetchOpportunity()
        // updateComment(newComments)
        setToggleEdit({})
        notify('Edited comment', 'success')
        // initFetch()
      } else {
        console.log('COMMENT ERR', response?.data?.message)
        notify('Failed to edit comment', 'error')
      }
    } catch (error) {
      console.log({ error })
    } finally {
      setCommentFlag(false)
      setCommentFlag(false)
    }
  }

  const deleteComment = async (index: number) => {
    setCommentFlag(true)

    const newComments = deleteElementAtIndex(comments, index)
    const res = await deleteCommentApi({
      id: comments[index]._id!,
      memberId: currentMember._id!, //UserId##
      oppId: oppId!,
    })
    if (isSuccess(res)) {
      notify('Deleted comment', 'success')
      // updateComment(newComments)
      setCommentFlag(false)
      setCommentFlag1(true)
    } else {
      console.log('DELETE COMMENT ERR', res?.data)
      notify('Failed to delete comment', 'error')
      setCommentFlag(false)
      setCommentFlag1(true)
    }
  }

  const updateComment = async (newComments: I_AllComments[]) => {
    // setUpdateLoading(true)
    // try {
    // const response = await updateOpportunity({ ...oppData, comments: [...newComments] } as any)
    // setUpdateLoading(false)
    setAllComments([...newComments])
    setEditing(-1)

    // since it reverses in func
    // fetchCommentsData(newComments.reverse())

    // --------------------------------my chnages-------------------------------
    initFetchOpportunity()
    // initFetch()
    // --------------------------------my chnages-------------------------------

    //   if (!isSuccess(response)) {
    //     throw new Error(response?.data?.message)
    //   } else console.log('UPDATED COMMENTS')
    // } catch (err: any) {
    //   // setUpdateLoading(false)
    //   console.log('Save STAGE ERR', err)
    //   notify(err?.message ?? 'Failed to update data!', 'error')
    // }
  }

  // useEffect(() => {
  //   setAllComments(comments)
  // }, [comments])
  function isLoading(flag1: boolean, flag2: boolean) {
    return flag1 === false && flag2 === false
  }

  return !isLoading(commentFlag, commentFlag1) ? (
    <>
      <SharedStyled.Skeleton custWidth="100%" custHeight={'51px'} custMarginTop={'50px'} />
    </>
  ) : (
    <SharedStyled.FlexBox flexDirection="column" gap="12px" width="100%">
      <div>
        <Formik
          initialValues={initComment}
          onSubmit={submitComment}
          validationSchema={commentSchema}
          validateOnChange={true}
          validateOnBlur={false}
          enableReinitialize={true}
        >
          {({ touched, errors, resetForm, values, setFieldValue, handleSubmit }) => {
            useEffect(() => {
              if (values?.comment) {
                localStorage.setItem(`comment-${oppId}`, JSON.stringify(values?.comment))
              }
            }, [values?.comment])
            return (
              <Form onSubmit={handleSubmit}>
                <SharedStyled.FlexBox flexDirection="column" alignItems="flex-start">
                  <div style={{ width: '100%' }}>
                    <SharedStyled.FlexRow>
                      <SharedStyled.ContentHeader textAlign="start" as={'h3'}>
                        Comments
                      </SharedStyled.ContentHeader>

                      <Toggle
                        title="Hide Contact Comments"
                        className="text bold"
                        customStyles={{
                          margin: '0px',
                          justifyContent: 'flex-end',
                        }}
                        titleStyle={{
                          fontFamily: Nue.regular,
                        }}
                        isToggled={isToggleOn}
                        onToggle={() => {
                          setIsToggleOn((prev) => !prev)
                        }}
                      />
                    </SharedStyled.FlexRow>
                    <SharedStyled.TextArea
                      component="textarea"
                      as={Field}
                      name="comment"
                      marginTop="8px"
                      height="52px"
                      stateName="comment"
                      labelName="Comment"
                      placeholder="Add comment"
                      error={touched.comment && errors.comment ? true : false}
                    />
                  </div>
                  <SharedStyled.FlexRow margin="10px 0 0 0">
                    <Button
                      type="button"
                      className="fit"
                      onClick={() => handleSubmit()}
                      disabled={!values.comment?.trim()}
                    >
                      Save Comment
                    </Button>
                  </SharedStyled.FlexRow>
                </SharedStyled.FlexBox>
              </Form>
            )
          }}
        </Formik>
      </div>
      <SharedStyled.FlexBox overflow="auto" flexDirection="column" width="100%" maxHeight="380px">
        {comments
          ?.filter((comment) => (isToggleOn ? !comment?.contactId : true))
          ?.sort((a: any, b: any) => new Date(b?.createdAt)?.getTime() - new Date(a?.createdAt)?.getTime())
          .map((comment: any, idx) => (
            <Styled.CommentsContainer key={idx}>
              {toggleEdit[`${idx}`] ? (
                <Formik
                  initialValues={{ comment: comment.body, _id: comment?._id }}
                  onSubmit={editCommentComplete}
                  validationSchema={commentSchema}
                  validateOnChange={true}
                  validateOnBlur={false}
                  enableReinitialize={true}
                >
                  {({ touched, errors, resetForm, values, setFieldValue, handleSubmit }) => (
                    <Form onSubmit={handleSubmit}>
                      <SharedStyled.FlexBox flexDirection="column" alignItems="flex-start">
                        <div style={{ width: '100%' }}>
                          <SharedStyled.TextArea
                            component="textarea"
                            as={Field}
                            name="comment"
                            marginTop="8px"
                            height="52px"
                            stateName="comment"
                            labelName="Comment"
                            placehold="Enter new comment"
                            error={touched.comment && errors.comment ? true : false}
                          />
                        </div>
                        {/* <Styled.EditContainer onClick={() => toggleTableData(`${idx}`)}>
                          <CrossIcon />
                        </Styled.EditContainer> */}
                        <SharedStyled.FlexBox width="100%" gap="8px" marginTop="10px">
                          <Button type="submit" className="fit">
                            Update
                          </Button>
                          <Button type="button" className="fit" onClick={() => toggleTableData(`${idx}`)}>
                            Cancel
                          </Button>
                        </SharedStyled.FlexBox>
                      </SharedStyled.FlexBox>
                    </Form>
                  )}
                </Formik>
              ) : (
                <>
                  <SharedStyled.FlexBox flexDirection="column">
                    <SharedStyled.FlexBox justifyContent="flex-start" margin="0 0 4px 0">
                      <SharedStyled.Text fontSize="14px" fontWeight="bold">
                        {' '}
                        <span style={{ textTransform: 'capitalize' }} className="bold">
                          {comment?.name || 'user'}
                        </span>
                        <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                          &emsp;{dayjsFormat(comment?.createdAt, 'M/D/YY hh:mm A')}
                        </SharedStyled.Text>
                      </SharedStyled.Text>

                      <Pill
                        margin="0 0 0 10px"
                        numVal={comment?.oppId ? comment?.num : undefined}
                        path={comment?.contactId ? `/contact/profile/${comment?.contactId}/false` : ''}
                        text={comment?.oppId ? `${comment?.PO}-${comment?.num}` : 'Contact'}
                      />

                      <div>
                        <SharedStyled.FlexBox>
                          {comment?.createdBy === currentMember._id &&
                          isWithinHour(comment?.createdAt) &&
                          comment?.edits?.length < 5 ? (
                            <>
                              <Styled.EditContainer onClick={() => toggleTableData(`${idx}`)}>
                                {/* <EditIcon /> */}
                                <SharedStyled.Text
                                  textDecoration="underline"
                                  margin="0 10px 0 0"
                                  fontSize="12px"
                                  color={colors.blueLight}
                                >
                                  edit
                                </SharedStyled.Text>
                              </Styled.EditContainer>

                              {/* <SharedStyled.Text margin="0 0 0 15px" fontSize="12px">
                            {comment?.edits?.length < 5
                              ? `${5 - Number(comment?.edits?.length || 0)} ${
                                5 - Number(comment?.edits?.length || 0) === 1 ? 'edit' : 'edits'
                              } left`
                              : null}
                            </SharedStyled.Text> */}
                            </>
                          ) : null}
                          {comment?.edits?.length > 0 ? (
                            <SharedStyled.Text
                              margin="2px 0 0 0"
                              textAlign="right"
                              fontSize="12px"
                              color={colors.lightGrey}
                            >
                              edited
                            </SharedStyled.Text>
                          ) : null}
                        </SharedStyled.FlexBox>
                      </div>
                    </SharedStyled.FlexBox>

                    {/* {toggleEdit[`${idx}`] ? (
                      <>
                       
                      </>
                    ) : ( */}
                    <span className="comment">
                      <ReactMarkdown>
                        {comment?.body?.replace(/\n/g, '  \n') || comment?.oppNotes?.replace(/\n/g, '  \n')}
                      </ReactMarkdown>
                    </span>
                    {/* )} */}
                  </SharedStyled.FlexBox>

                  <Styled.MarginBox>
                    {(positionDetails?.symbol === 'Owner' || positionDetails?.symbol === 'Admin') &&
                    comment?.edits?.length > 0 ? (
                      <div onClick={() => toggleTableData(`${idx} history`)}>
                        {!toggleEdit[`${idx} history`] ? (
                          <Styled.EditContainer>
                            <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                              show edits
                            </SharedStyled.Text>
                          </Styled.EditContainer>
                        ) : (
                          <Styled.EditContainer>
                            <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                              hide edits
                            </SharedStyled.Text>
                          </Styled.EditContainer>
                        )}
                      </div>
                    ) : (
                      comment?.createdBy === currentMember._id &&
                      comment?.edits?.length > 0 && (
                        <div onClick={() => toggleTableData(`${idx} history`)}>
                          {/* <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                                  edited -{' '}
                                </SharedStyled.Text> */}
                          {!toggleEdit[`${idx} history`] ? (
                            <Styled.EditContainer>
                              <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                                show edits
                              </SharedStyled.Text>
                            </Styled.EditContainer>
                          ) : (
                            <Styled.EditContainer>
                              <SharedStyled.Text textDecoration="underline" fontSize="12px" color={colors.lightGrey}>
                                hide edits
                              </SharedStyled.Text>
                            </Styled.EditContainer>
                          )}
                        </div>
                      )
                    )}
                  </Styled.MarginBox>

                  {toggleEdit[`${idx} history`] ? (
                    <Styled.MarginBox>
                      {/* <SharedStyled.Text fontSize="14px" fontWeight="bold">
                        {' '}
                        <span style={{ textTransform: 'capitalize' }} className="bold">
                          History
                        </span>
                      </SharedStyled.Text> */}

                      {comment?.edits?.length > 0
                        ? comment?.edits?.map((v: any, editIdx: number) => (
                            <SharedStyled.FlexBox key={editIdx} justifyContent="space-between" margin="0 0 10px 0">
                              <span className="comment" style={{ maxWidth: '70%' }}>
                                {/* <ReactMarkdown remarkPlugins={[remarkGfm]}>
                                  {v?.edit?.replace(/\n/g, '  \n')}
                                </ReactMarkdown> */}
                                <TextDiffViewer
                                  original={v?.edit}
                                  edited={editIdx === 0 ? comment?.body : comment?.edits[editIdx - 1]?.edit}
                                />
                              </span>
                              <SharedStyled.Text fontSize="12px" color={colors.lightGrey}>
                                {dayjsFormat(v?.editedAt, 'M/D/YY hh:mm A')}
                              </SharedStyled.Text>
                            </SharedStyled.FlexBox>
                          ))
                        : null}
                    </Styled.MarginBox>
                  ) : null}
                  {/* {comment?.edits?.length > 0 ? (
                    <SharedStyled.Text textAlign="right" fontSize="12px" color={colors.lightGrey}>
                      edited
                    </SharedStyled.Text>
                  ) : null} */}
                </>
              )}
            </Styled.CommentsContainer>
          ))}{' '}
        {comments.length === 0 ? <SharedStyled.Text>No comments found!</SharedStyled.Text> : null}
      </SharedStyled.FlexBox>
    </SharedStyled.FlexBox>
  )
}

export default React.memo(Comments)
